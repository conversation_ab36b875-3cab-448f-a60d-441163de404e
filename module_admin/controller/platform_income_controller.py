from fastapi import APIRouter, Depends, Request, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.company_transaction_vo import (
    CompanyTransactionModel,
    CompanyTransactionPageQueryModel,
    CompanyTransactionQueryModel
)
from module_admin.service.platform_income_service import PlatformIncomeService
from module_admin.service.login_service import LoginService, CurrentUserModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


platformIncomeController = APIRouter(
    prefix='/finance/platform-income',
    dependencies=[Depends(LoginService.get_current_user)]
)


@platformIncomeController.get(
    '/list', 
    response_model=PageResponseModel, 
    dependencies=[Depends(CheckUserInterfaceAuth('finance:transaction:list'))]
)
async def get_platform_income_list(
    request: Request,
    transaction_page_query: CompanyTransactionPageQueryModel = Depends(CompanyTransactionPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取平台收入流水列表
    
    :param request: 请求对象
    :param transaction_page_query: 分页查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 平台收入流水列表
    """
    # 设置排除的业务类型（增加提现申请和代扣个税）
    transaction_page_query.exclude_business_types = 'SOFTWARE_RENEWAL,RECHARGE,WITHDRAWAL,TAX_DEDUCTION'
    # 强制设置交易类型为支出（相对于平台是收入）
    transaction_page_query.transaction_type = 2
    
    # 获取分页数据
    transaction_page_query_result = await PlatformIncomeService.get_platform_income_list_services(
        query_db, transaction_page_query, is_page=True
    )
    logger.info('获取平台收入流水列表成功')

    return ResponseUtil.success(model_content=transaction_page_query_result)


@platformIncomeController.post(
    '/export', 
    dependencies=[Depends(CheckUserInterfaceAuth('finance:transaction:export'))]
)
@Log(title='平台收入流水', business_type=BusinessType.EXPORT)
async def export_platform_income_list(
    request: Request,
    transaction_page_query: CompanyTransactionPageQueryModel = Form(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    导出平台收入流水列表
    
    :param request: 请求对象
    :param transaction_page_query: 查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 导出文件
    """
    # 设置排除的业务类型（增加提现申请和代扣个税）
    transaction_page_query.exclude_business_types = 'SOFTWARE_RENEWAL,RECHARGE,WITHDRAWAL,TAX_DEDUCTION'
    # 强制设置交易类型为支出（相对于平台是收入）
    transaction_page_query.transaction_type = 2
    
    # 获取全量数据
    transaction_query_result = await PlatformIncomeService.get_platform_income_list_services(
        query_db, transaction_page_query, is_page=False
    )
    transaction_export_result = await PlatformIncomeService.export_platform_income_list_services(transaction_query_result)
    logger.info('导出平台收入流水列表成功')

    return ResponseUtil.streaming(data=bytes2file_response(transaction_export_result))


@platformIncomeController.get(
    '/statistics',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:transaction:list'))]
)
async def get_platform_income_statistics(
    request: Request,
    transaction_query: CompanyTransactionQueryModel = Depends(CompanyTransactionQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取平台收入统计数据（根据筛选条件动态统计）

    :param request: 请求对象
    :param transaction_query: 查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 平台收入统计数据
    """
    # 如果用户没有选择特定的业务类型，则使用默认的排除逻辑
    if not transaction_query.business_type:
        transaction_query.exclude_business_types = 'SOFTWARE_RENEWAL,RECHARGE,WITHDRAWAL,TAX_DEDUCTION'
    # 强制设置交易类型为支出（相对于平台是收入）
    transaction_query.transaction_type = 2

    # 获取统计数据（使用筛选条件）
    statistics_result = await PlatformIncomeService.get_platform_income_statistics_services(
        query_db, transaction_query
    )
    logger.info('获取平台收入统计数据成功')

    return ResponseUtil.success(data=statistics_result)



