<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司名称" prop="companyUuid">
        <el-select
          v-model="queryParams.companyUuid"
          placeholder="请选择公司"
          clearable
          filterable
          style="width: 200px"
        >
          <el-option
            v-for="company in companyList"
            :key="company.id"
            :label="company.name"
            :value="company.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="流水号" prop="transactionNo">
        <el-input
          v-model="queryParams.transactionNo"
          placeholder="请输入交易流水号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="queryParams.businessType" placeholder="请选择业务类型" clearable>
          <el-option label="软件购买" value="SOFTWARE_PURCHASE" />
          <el-option label="购买保险" value="INSURANCE_PURCHASE" />
          <el-option label="保险续费" value="INSURANCE_RENEWAL" />
          <el-option label="培训服务" value="TRAINING_SERVICE" />
          <el-option label="认证服务" value="CERTIFICATION_SERVICE" />
          <el-option label="退款" value="REFUND" />
          <el-option label="佣金结算" value="COMMISSION_SETTLEMENT" />
          <el-option label="账户开设费" value="ACCOUNT_OPENING_FEE" />
        </el-select>
      </el-form-item>

      <el-form-item label="操作人" prop="operatorName">
        <el-input
          v-model="queryParams.operatorName"
          placeholder="请输入操作人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="交易状态" prop="transactionStatus">
        <el-select v-model="queryParams.transactionStatus" placeholder="请选择交易状态" clearable>
          <el-option label="待处理" value="PENDING" />
          <el-option label="成功" value="SUCCESS" />
          <el-option label="失败" value="FAILED" />
          <el-option label="已取消" value="CANCELLED" />
        </el-select>
      </el-form-item>
      <el-form-item label="交易时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 收入统计展示 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>收入统计</span>
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                <div class="tooltip-content">
                  <div class="tooltip-title">统计规则说明：</div>
                  <div class="tooltip-item">• 业务类型：可选择特定类型统计，未选择时排除软件续费、余额充值、提现申请和代扣个税</div>
                  <div class="tooltip-item">• 交易类型：仅统计支出类型（相对平台为收入）</div>
                  <div class="tooltip-item">• 交易状态：仅统计成功的交易</div>
                  <div class="tooltip-item">• 统计范围：当前筛选条件下的所有符合记录</div>
                </div>
              </div>
              <i class="el-icon-question tooltip-icon"></i>
            </el-tooltip>
          </div>
          <div class="text item">
            <span class="label">总金额：</span>
            <span class="amount income">{{ formatAmount(totalIncome) }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['finance:transaction:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportSelected"
          v-hasPermi="['finance:transaction:export']"
          :disabled="multiple"
        >导出选择</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="transactionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="流水号" align="center" prop="transactionNo" width="180" />
      <el-table-column label="公司名称" align="center" width="150">
        <template slot-scope="scope">
          {{ getCompanyNameByUuid(scope.row.companyUuid) }}
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="businessTypeName" width="120" />
      <el-table-column label="交易类型" align="center" prop="transactionTypeName" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.transactionType === 1 ? 'success' : 'danger'">
            {{ scope.row.transactionTypeName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="交易金额" align="center" prop="amount" width="120">
        <template slot-scope="scope">
          <span :class="scope.row.transactionType === 1 ? 'text-success' : 'text-danger'">
            {{ scope.row.transactionType === 1 ? '+' : '-' }}{{ scope.row.amount }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="交易前余额" align="center" prop="balanceBefore" width="120" />
      <el-table-column label="交易后余额" align="center" prop="balanceAfter" width="120" />
      <el-table-column label="操作人" align="center" prop="operatorName" width="100" />
      <el-table-column label="交易状态" align="center" prop="transactionStatus" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.transactionStatus === 'SUCCESS' ? 'success' : 
                         scope.row.transactionStatus === 'PENDING' ? 'warning' : 
                         scope.row.transactionStatus === 'FAILED' ? 'danger' : 'info'">
            {{ getStatusText(scope.row.transactionStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="交易描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="交易时间" align="center" prop="transactionTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.transactionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['finance:transaction:query']"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 流水详情对话框 -->
    <el-dialog title="平台收入详情" :visible.sync="open" width="800px" append-to-body>
      <el-descriptions :column="2" border v-if="form">
        <el-descriptions-item label="流水号">{{ form.transactionNo }}</el-descriptions-item>
        <el-descriptions-item label="公司名称">{{ getCompanyNameByUuid(form.companyUuid) }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">{{ form.businessTypeName }}</el-descriptions-item>
        <el-descriptions-item label="交易类型">
          <el-tag :type="form.transactionType === 1 ? 'success' : 'danger'">
            {{ form.transactionTypeName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="交易金额">
          <span :class="form.transactionType === 1 ? 'text-success' : 'text-danger'">
            {{ form.transactionType === 1 ? '+' : '-' }}{{ form.amount }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="交易前余额">{{ form.balanceBefore }}</el-descriptions-item>
        <el-descriptions-item label="交易后余额">{{ form.balanceAfter }}</el-descriptions-item>
        <el-descriptions-item label="关联订单号">{{ form.relatedOrderNo || '无' }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ form.payType || '无' }}</el-descriptions-item>
        <el-descriptions-item label="外部交易ID">{{ form.externalTransactionId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ form.operatorName }}</el-descriptions-item>
        <el-descriptions-item label="交易状态">
          <el-tag :type="form.transactionStatus === 'SUCCESS' ? 'success' : 
                         form.transactionStatus === 'PENDING' ? 'warning' : 
                         form.transactionStatus === 'FAILED' ? 'danger' : 'info'">
            {{ getStatusText(form.transactionStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="交易时间">{{ parseTime(form.transactionTime) }}</el-descriptions-item>
        <el-descriptions-item label="交易描述" :span="2">{{ form.description }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ form.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPlatformIncomeTransaction, getPlatformIncomeTransaction, getPlatformIncomeStatistics } from "@/api/finance/platform-income";
import { listCompany } from "@/api/merchant/company";

export default {
  name: "PlatformIncome",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 流水表格数据
      transactionList: [],
      // 总收入金额
      totalIncome: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 公司列表数据
      companyList: [],
      // 公司UUID到名称的映射
      companyMap: {},
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyUuid: null,
        transactionNo: null,
        businessType: null,
        transactionType: 2, // 固定为支出类型（相对于平台是收入）
        operatorName: null,
        transactionStatus: null,
        beginTime: null,
        endTime: null,
        excludeBusinessTypes: 'SOFTWARE_RENEWAL,RECHARGE,WITHDRAWAL,TAX_DEDUCTION' // 排除软件续费、余额充值、提现申请和代扣个税
      },
      // 表单参数
      form: {}
    };
  },
  created() {
    this.getCompanyList();
    this.getList();
  },
  methods: {
    /** 获取公司列表 */
    getCompanyList() {
      listCompany({ pageNum: 1, pageSize: 1000 }).then(response => {
        this.companyList = response.rows || [];
        // 创建UUID到名称的映射
        this.companyMap = {};
        this.companyList.forEach(company => {
          this.companyMap[company.id] = company.name;
        });
      });
    },
    /** 根据UUID获取公司名称 */
    getCompanyNameByUuid(uuid) {
      return this.companyMap[uuid] || uuid;
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'SUCCESS': '成功',
        'PENDING': '待处理',
        'FAILED': '失败',
        'CANCELLED': '已取消'
      };
      return statusMap[status] || status;
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查询流水列表 */
    getList() {
      this.loading = true;
      this.queryParams.beginTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];

      // 同时获取列表数据和统计数据
      Promise.all([
        listPlatformIncomeTransaction(this.queryParams),
        this.getStatistics()
      ]).then(([listResponse]) => {
        this.transactionList = listResponse.rows;
        this.total = listResponse.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      // 构建统计查询参数，排除分页信息
      const statisticsParams = {
        companyUuid: this.queryParams.companyUuid,
        transactionNo: this.queryParams.transactionNo,
        businessType: this.queryParams.businessType,
        operatorName: this.queryParams.operatorName,
        transactionStatus: this.queryParams.transactionStatus,
        beginTime: this.queryParams.beginTime,
        endTime: this.queryParams.endTime
      };

      return getPlatformIncomeStatistics(statisticsParams).then(response => {
        this.totalIncome = response.data.totalAmount || 0;
        return response;
      }).catch(error => {
        console.error('获取统计数据失败:', error);
        this.totalIncome = 0;
        return Promise.resolve();
      });
    },

    /** 格式化金额显示 */
    formatAmount(amount) {
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2
      }).format(amount);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.excludeBusinessTypes = 'SOFTWARE_RENEWAL,RECHARGE,WITHDRAWAL,TAX_DEDUCTION'; // 重置时保持排除条件
      this.queryParams.transactionType = 2; // 重置时保持支出类型过滤
      this.handleQuery();
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.reset();
      const transactionId = row.id;
      getPlatformIncomeTransaction(transactionId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "平台收入详情";
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      // 获取当前查询条件下的数据并添加公司名称
      this.loading = true;
      const exportParams = { ...this.queryParams };
      exportParams.beginTime = this.dateRange[0];
      exportParams.endTime = this.dateRange[1];

      // 先获取数据，然后添加公司名称字段
      listPlatformIncomeTransaction(exportParams).then(response => {
        const exportData = response.rows.map(item => ({
          ...item,
          companyName: this.getCompanyNameByUuid(item.companyUuid)
        }));

        this.download('/finance/platform-income/export', {
          ...exportParams,
          exportData: JSON.stringify(exportData)
        }, `platform_income_${new Date().getTime()}.xlsx`);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 导出选中按钮操作 */
    handleExportSelected() {
      if (this.ids.length === 0) {
        this.$message.warning('请选择要导出的数据');
        return;
      }

      // 获取选中的数据并添加公司名称
      const selectedData = this.transactionList
        .filter(item => this.ids.includes(item.id))
        .map(item => ({
          ...item,
          companyName: this.getCompanyNameByUuid(item.companyUuid)
        }));

      this.download('/finance/platform-income/export', {
        ids: this.ids.join(','),
        exportData: JSON.stringify(selectedData)
      }, `platform_income_selected_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>

<style scoped>
.text-success {
  color: #67C23A;
}
.text-danger {
  color: #F56C6C;
}
.box-card {
  margin-bottom: 20px;
}
.text.item {
  font-size: 16px;
  line-height: 24px;
}
.label {
  font-weight: bold;
  color: #606266;
}
.amount.income {
  font-size: 20px;
  font-weight: bold;
  color: #67C23A;
}
.tooltip-icon {
  margin-left: 8px;
  color: #909399;
  cursor: pointer;
  font-size: 14px;
}
.tooltip-icon:hover {
  color: #409EFF;
}
.tooltip-content {
  max-width: 300px;
  line-height: 1.6;
}
.tooltip-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #FFFFFF;
}
.tooltip-item {
  margin-bottom: 4px;
  color: #E4E7ED;
}
</style>
