<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司名称" prop="companyUuid">
        <el-select v-model="queryParams.companyUuid" placeholder="请选择公司" clearable filterable>
          <el-option
            v-for="company in companyOptions"
            :key="company.id"
            :label="company.name"
            :value="company.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请单号" prop="withdrawalNo">
        <el-input
          v-model="queryParams.withdrawalNo"
          placeholder="请输入申请单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="提现类型" prop="withdrawalType">
        <el-select v-model="queryParams.withdrawalType" placeholder="请选择提现类型" clearable>
          <el-option label="自行开票" :value="1" />
          <el-option label="零工提现" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择申请状态" clearable>
          <el-option label="待审核" value="PENDING" />
          <el-option label="已通过" value="APPROVED" />
          <el-option label="已拒绝" value="REJECTED" />
          <el-option label="处理中" value="PROCESSING" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="已取消" value="CANCELLED" />
          <el-option label="处理驳回" value="REJECTED_PROCESS" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请人" prop="applicantName">
        <el-input
          v-model="queryParams.applicantName"
          placeholder="请输入申请人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="reviewerName">
        <el-input
          v-model="queryParams.reviewerName"
          placeholder="请输入审核人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处理人" prop="processorName">
        <el-input
          v-model="queryParams.processorName"
          placeholder="请输入处理人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['finance:withdrawal:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="withdrawalList">
      <el-table-column label="申请单号" align="center" prop="withdrawalNo" width="180" />
      <el-table-column label="公司名称" align="center" prop="companyName" width="150" />
      <el-table-column label="提现类型" align="center" prop="withdrawalTypeName" width="100" />
      <el-table-column label="申请金额" align="center" prop="applyAmount" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.applyAmount }}元</span>
        </template>
      </el-table-column>
      <el-table-column label="手续费" align="center" prop="feeAmount" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.feeAmount }}元</span>
        </template>
      </el-table-column>
      <el-table-column label="实际到账" align="center" prop="actualAmount" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.actualAmount }}元</span>
        </template>
      </el-table-column>
      <el-table-column label="申请状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ scope.row.statusName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applicantName" width="120" />
      <el-table-column label="审核人" align="center" prop="reviewerName" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.reviewerName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="processorName" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.processorName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="applyTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleReview(scope.row, 'approve')"
            v-if="scope.row.status === 'PENDING'"
            v-hasPermi="['finance:withdrawal:review']"
          >通过</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleReview(scope.row, 'reject')"
            v-if="scope.row.status === 'PENDING'"
            v-hasPermi="['finance:withdrawal:review']"
          >拒绝</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-money"
            @click="handleProcess(scope.row)"
            v-if="scope.row.status === 'APPROVED'"
            v-hasPermi="['finance:withdrawal:process']"
          >处理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleCancel(scope.row)"
            v-if="['PENDING', 'APPROVED'].includes(scope.row.status)"
            v-hasPermi="['finance:withdrawal:cancel']"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog title="提现申请详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请单号">{{ form.withdrawalNo }}</el-descriptions-item>
        <el-descriptions-item label="公司名称">{{ form.companyName }}</el-descriptions-item>
        <el-descriptions-item label="提现类型">{{ form.withdrawalTypeName }}</el-descriptions-item>
        <el-descriptions-item label="申请状态">
          <el-tag :type="getStatusTagType(form.status)">
            {{ form.statusName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请金额">{{ form.applyAmount }}元</el-descriptions-item>
        <el-descriptions-item label="手续费">{{ form.feeAmount }}元</el-descriptions-item>
        <el-descriptions-item label="实际到账金额">{{ form.actualAmount }}元</el-descriptions-item>
        <el-descriptions-item label="开户银行">{{ form.bankName }}</el-descriptions-item>
        <el-descriptions-item label="银行账号">{{ form.bankAccount }}</el-descriptions-item>
        <el-descriptions-item label="开户人">{{ form.accountHolder }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ form.applicantName }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(form.applyTime) }}</el-descriptions-item>
        <el-descriptions-item label="申请原因" :span="2">{{ form.applyReason }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ form.remark || '无' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 关联资金流水 -->
      <el-divider content-position="left">关联资金流水</el-divider>
      <div v-if="sourceTransactionList && sourceTransactionList.length > 0">
        <el-table :data="sourceTransactionList" border style="width: 100%">
          <el-table-column prop="transactionNo" label="资金流水号" width="180"></el-table-column>
          <el-table-column prop="amount" label="金额" width="120">
            <template slot-scope="scope">
              <span :class="scope.row.transactionType === 1 ? 'text-success' : 'text-danger'">
                {{ scope.row.transactionType === 1 ? '+' : '-' }}{{ scope.row.amount }}元
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.remark || '无' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-empty v-else description="无关联资金流水" :image-size="60"></el-empty>

      <!-- 开票信息 -->
      <el-divider content-position="left">开票信息</el-divider>
      <!-- 当invoiceInfo是图片URL时，以图片形式展示 -->
      <div v-if="form.withdrawalType === 1 && form.invoiceInfo && isImageUrl(form.invoiceInfo)" class="invoice-image-container">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="发票信息">
            <el-image
              :src="form.invoiceInfo"
              :preview-src-list="[form.invoiceInfo]"
              fit="contain"
              style="width: 200px; height: 150px; cursor: pointer;"
              :lazy="true"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
                <p>图片加载失败</p>
              </div>
            </el-image>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 当invoiceInfo是对象时，以表格形式展示 -->
      <el-descriptions v-else-if="form.withdrawalType === 1 && form.invoiceInfo && typeof form.invoiceInfo === 'object'" :column="2" border>
        <el-descriptions-item label="发票类型">{{ getInvoiceTypeName(form.invoiceInfo.invoiceType) }}</el-descriptions-item>
        <el-descriptions-item label="发票抬头">{{ form.invoiceInfo.invoiceTitle }}</el-descriptions-item>
        <el-descriptions-item label="纳税人识别号">{{ form.invoiceInfo.taxNumber }}</el-descriptions-item>
        <el-descriptions-item label="开户行及账号">{{ form.invoiceInfo.invoiceBankInfo }}</el-descriptions-item>
        <el-descriptions-item label="注册地址及电话" :span="2">{{ form.invoiceInfo.registeredAddress }}</el-descriptions-item>
        <el-descriptions-item label="发票内容" :span="2">{{ form.invoiceInfo.invoiceContent }}</el-descriptions-item>
      </el-descriptions>
      <el-empty v-else-if="form.withdrawalType === 1" description="暂无开票信息" :image-size="60"></el-empty>
      <el-empty v-else description="零工提现无需开票" :image-size="60"></el-empty>

      <!-- 审核信息 -->
      <el-divider content-position="left">审核信息</el-divider>
      <el-descriptions :column="2" border v-if="['APPROVED', 'REJECTED', 'PROCESSING', 'COMPLETED', 'REJECTED_PROCESS'].includes(form.status)">
        <el-descriptions-item label="审核人">{{ form.reviewerName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="审核时间">{{ form.reviewTime ? parseTime(form.reviewTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="审核意见" :span="2">{{ form.reviewComment || '无' }}</el-descriptions-item>
      </el-descriptions>
      <el-empty v-else description="暂无审核信息" :image-size="60"></el-empty>

      <!-- 处理信息 -->
      <el-divider content-position="left">处理信息</el-divider>
      <el-descriptions :column="2" border v-if="['PROCESSING', 'COMPLETED', 'REJECTED_PROCESS'].includes(form.status)">
        <el-descriptions-item label="处理人">{{ form.processorName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="处理时间">{{ form.processTime ? parseTime(form.processTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="完成时间" v-if="form.status === 'COMPLETED'">{{ form.completionTime ? parseTime(form.completionTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="交易流水号" v-if="form.status === 'COMPLETED'">{{ form.transactionId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="驳回原因" v-if="form.status === 'REJECTED_PROCESS'" :span="2">{{ form.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <el-empty v-else description="暂无处理信息" :image-size="60"></el-empty>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog :title="reviewTitle" :visible.sync="reviewOpen" width="500px" append-to-body>
      <el-form ref="reviewForm" :model="reviewForm" :rules="reviewRules" label-width="100px">
        <el-form-item label="申请单号">
          <el-input v-model="reviewForm.withdrawalNo" disabled />
        </el-form-item>
        <el-form-item label="申请金额">
          <el-input v-model="reviewForm.applyAmount" disabled>
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="审核意见" prop="reviewComment">
          <el-input
            v-model="reviewForm.reviewComment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelReview">取 消</el-button>
        <el-button type="primary" :loading="reviewLoading" @click="submitReview">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 处理对话框 -->
    <el-dialog title="处理提现申请" :visible.sync="processOpen" width="800px" append-to-body>
      <!-- 基本信息 -->
      <el-descriptions class="margin-top" :column="2" border>
        <el-descriptions-item label="申请单号">{{ processForm.withdrawalNo || '无' }}</el-descriptions-item>
        <el-descriptions-item label="公司名称">{{ processForm.companyName || '无' }}</el-descriptions-item>
        <el-descriptions-item label="提现类型">{{ processForm.withdrawalTypeName || '无' }}</el-descriptions-item>
        <el-descriptions-item label="申请状态">
          <el-tag :type="getStatusTagType(processForm.status)">
            {{ processForm.statusName || '无' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请人">{{ processForm.applicantName || '无' }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(processForm.applyTime) || '无' }}</el-descriptions-item>
        <el-descriptions-item label="申请原因" :span="2">{{ processForm.applyReason || '无' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 提现账户信息 -->
      <el-divider content-position="left">提现账户信息</el-divider>
      <el-descriptions class="margin-top" :column="2" border>
        <el-descriptions-item label="开户银行">{{ processForm.bankName || '无' }}</el-descriptions-item>
        <el-descriptions-item label="银行账号">{{ processForm.bankAccount || '无' }}</el-descriptions-item>
        <el-descriptions-item label="开户人">{{ processForm.accountHolder || '无' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 金额信息 -->
      <el-divider content-position="left">金额信息</el-divider>
      <el-descriptions class="margin-top" :column="2" border>
        <el-descriptions-item label="申请金额">{{ processForm.applyAmount }}元</el-descriptions-item>
        <el-descriptions-item label="手续费">{{ processForm.feeAmount }}元</el-descriptions-item>
        <el-descriptions-item label="实际到账金额">{{ processForm.actualAmount }}元</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ processForm.remark || '无' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 操作表单 -->
      <el-form ref="processForm" :model="processForm" :rules="processRules" label-width="100px" style="margin-top: 20px;">
        <el-form-item label="交易流水号" prop="transactionId" v-if="processForm.action === 'complete'">
          <el-input
            v-model="processForm.transactionId"
            placeholder="请输入银行交易流水号"
          />
        </el-form-item>
        <el-form-item label="处理备注" :prop="processForm.action === 'reject' ? 'remark' : ''">
          <el-input
            v-model="processForm.remark"
            type="textarea"
            :rows="3"
            :placeholder="processForm.action === 'reject' ? '请输入驳回原因（必填）' : '请输入处理备注（可选）'"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelProcess">取 消</el-button>
        <el-button type="warning" :loading="processLoading" @click="submitReject">驳 回</el-button>
        <el-button type="primary" :loading="processLoading" @click="submitComplete">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWithdrawal, getWithdrawal, reviewWithdrawal, processWithdrawal, cancelWithdrawal } from "@/api/finance/withdrawal";
import { listCompany } from "@/api/merchant/company";
import { getTransaction } from "@/api/finance/transaction";

export default {
  name: "WithdrawalManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 提现申请表格数据
      withdrawalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情对话框
      detailOpen: false,
      // 审核对话框
      reviewOpen: false,
      reviewLoading: false,
      reviewTitle: '',
      // 处理对话框
      processOpen: false,
      processLoading: false,
      // 日期范围
      dateRange: [],
      // 公司选项
      companyOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyUuid: null,
        withdrawalNo: null,
        withdrawalType: null,
        status: null,
        applicantName: null,
        reviewerName: null,
        processorName: null,
        beginTime: null,
        endTime: null
      },
      // 表单参数
      form: {},
      // 关联的资金流水编号列表
      sourceTransactionList: [],
      // 审核表单
      reviewForm: {
        withdrawalId: null,
        withdrawalNo: '',
        applyAmount: '',
        action: '',
        reviewComment: ''
      },
      // 处理表单
      processForm: {
        withdrawalId: null,
        withdrawalNo: '',
        companyName: '',
        withdrawalTypeName: '',
        status: '',
        statusName: '',
        applicantName: '',
        applyTime: '',
        applyReason: '',
        bankName: '',
        bankAccount: '',
        accountHolder: '',
        applyAmount: '',
        feeAmount: '',
        actualAmount: '',
        action: 'complete', // complete: 完成, reject: 驳回
        transactionId: '',
        remark: ''
      },
      // 审核表单校验
      reviewRules: {
        reviewComment: [
          { required: true, message: "审核意见不能为空", trigger: "blur" }
        ]
      },
      // 处理表单校验
      processRules: {
        transactionId: [
          { required: true, message: "交易流水号不能为空", trigger: "blur" }
        ],
        remark: [
          { required: true, message: "驳回原因不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getCompanyOptions();
  },
  methods: {
    /** 查询提现申请列表 */
    getList() {
      this.loading = true;
      this.queryParams.beginTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];
      listWithdrawal(this.queryParams).then(response => {
        this.withdrawalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取公司选项 */
    getCompanyOptions() {
      listCompany({ pageNum: 1, pageSize: 1000 }).then(response => {
        this.companyOptions = response.rows || [];
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },


    /** 导出按钮操作 */
    handleExport() {
      this.download('finance/withdrawal/export', {
        ...this.queryParams
      }, `withdrawal_${new Date().getTime()}.xlsx`)
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.reset();
      const withdrawalId = row.id;
      getWithdrawal(withdrawalId).then(response => {
        this.form = response.data;
        // 解析开票信息
        if (this.form.invoiceInfo && typeof this.form.invoiceInfo === 'string') {
          // 如果是图片URL，直接保持字符串格式
          if (this.isImageUrl(this.form.invoiceInfo)) {
            // 保持原样，不需要解析
          } else {
            // 尝试解析JSON
            try {
              this.form.invoiceInfo = JSON.parse(this.form.invoiceInfo);
            } catch (e) {
              console.error('解析开票信息失败:', e);
              // 如果解析失败，保持原字符串
            }
          }
        }

        // 处理关联的资金流水编号
        this.processSourceTransactionIds();

        this.detailOpen = true;
      });
    },
    /** 审核按钮操作 */
    handleReview(row, action) {
      this.resetReviewForm();
      this.reviewForm.withdrawalId = row.id;
      this.reviewForm.withdrawalNo = row.withdrawalNo;
      this.reviewForm.applyAmount = row.applyAmount + '元';
      this.reviewForm.action = action;
      this.reviewTitle = action === 'approve' ? '审核通过' : '审核拒绝';
      this.reviewOpen = true;
    },
    /** 重置审核表单 */
    resetReviewForm() {
      this.reviewForm = {
        withdrawalId: null,
        withdrawalNo: '',
        applyAmount: '',
        action: '',
        reviewComment: ''
      };
      if (this.$refs.reviewForm) {
        this.$refs.reviewForm.resetFields();
      }
    },
    /** 取消审核 */
    cancelReview() {
      this.reviewOpen = false;
      this.resetReviewForm();
    },
    /** 提交审核 */
    submitReview() {
      this.$refs["reviewForm"].validate(valid => {
        if (valid) {
          this.reviewLoading = true;
          const reviewData = {
            action: this.reviewForm.action,
            reviewComment: this.reviewForm.reviewComment
          };

          reviewWithdrawal(this.reviewForm.withdrawalId, reviewData).then(response => {
            this.$modal.msgSuccess("审核完成");
            this.reviewOpen = false;
            this.resetReviewForm();
            this.getList();
          }).catch(error => {
            console.error('审核失败:', error);
          }).finally(() => {
            this.reviewLoading = false;
          });
        }
      });
    },
    /** 处理按钮操作 */
    handleProcess(row) {
      this.resetProcessForm();
      this.processForm.withdrawalId = row.id;
      this.processForm.withdrawalNo = row.withdrawalNo;
      this.processForm.companyName = row.companyName || '';
      this.processForm.withdrawalTypeName = row.withdrawalTypeName || '';
      this.processForm.status = row.status;
      this.processForm.statusName = row.statusName || '';
      this.processForm.applicantName = row.applicantName || '';
      this.processForm.applyTime = row.applyTime;
      this.processForm.applyReason = row.applyReason || '';
      this.processForm.bankName = row.bankName || '';
      this.processForm.bankAccount = row.bankAccount || '';
      this.processForm.accountHolder = row.accountHolder || '';
      this.processForm.applyAmount = row.applyAmount; // 不添加单位，在模板中添加
      this.processForm.feeAmount = row.feeAmount; // 不添加单位，在模板中添加
      this.processForm.actualAmount = row.actualAmount; // 不添加单位，在模板中添加
      this.processForm.remark = row.remark || '';
      this.processOpen = true;
    },
    /** 重置处理表单 */
    resetProcessForm() {
      this.processForm = {
        withdrawalId: null,
        withdrawalNo: '',
        companyName: '',
        withdrawalTypeName: '',
        status: '',
        statusName: '',
        applicantName: '',
        applyTime: '',
        applyReason: '',
        bankName: '',
        bankAccount: '',
        accountHolder: '',
        applyAmount: '',
        feeAmount: '',
        actualAmount: '',
        action: 'complete',
        transactionId: '',
        remark: ''
      };
      if (this.$refs.processForm) {
        this.$refs.processForm.resetFields();
      }
    },
    /** 取消处理 */
    cancelProcess() {
      this.processOpen = false;
      this.resetProcessForm();
    },
    /** 提交完成处理 */
    submitComplete() {
      this.processForm.action = 'complete';
      this.$refs["processForm"].validate(valid => {
        if (valid) {
          this.processLoading = true;
          const processData = {
            action: 'complete',
            transactionId: this.processForm.transactionId,
            remark: this.processForm.remark
          };

          processWithdrawal(this.processForm.withdrawalId, processData).then(response => {
            this.$modal.msgSuccess("处理完成");
            this.processOpen = false;
            this.resetProcessForm();
            this.getList();
          }).catch(error => {
            console.error('处理失败:', error);
          }).finally(() => {
            this.processLoading = false;
          });
        }
      });
    },
    /** 提交驳回处理 */
    submitReject() {
      this.processForm.action = 'reject';
      // 驳回时不需要验证交易流水号，只验证备注
      if (!this.processForm.remark || this.processForm.remark.trim() === '') {
        this.$modal.msgError("请输入驳回原因");
        return;
      }

      this.$modal.confirm('确认要驳回这个提现申请吗？').then(() => {
        this.processLoading = true;
        const processData = {
          action: 'reject',
          remark: this.processForm.remark
        };

        processWithdrawal(this.processForm.withdrawalId, processData).then(response => {
          this.$modal.msgSuccess("驳回成功");
          this.processOpen = false;
          this.resetProcessForm();
          this.getList();
        }).catch(error => {
          console.error('驳回失败:', error);
        }).finally(() => {
          this.processLoading = false;
        });
      }).catch(() => {
        // 用户取消操作
      });
    },
    /** 取消申请 */
    handleCancel(row) {
      this.$modal.confirm('是否确认取消申请单号为"' + row.withdrawalNo + '"的提现申请？').then(function() {
        return cancelWithdrawal(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("取消成功");
      }).catch(() => {});
    },
    /** 表单重置 */
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'PENDING': 'warning',
        'APPROVED': 'success',
        'REJECTED': 'danger',
        'PROCESSING': 'primary',
        'COMPLETED': 'success',
        'CANCELLED': 'info',
        'REJECTED_PROCESS': 'danger'
      };
      return statusMap[status] || 'info';
    },
    /** 获取发票类型名称 */
    getInvoiceTypeName(type) {
      const typeMap = {
        'VAT_SPECIAL': '增值税专用发票',
        'VAT_ORDINARY': '增值税普通发票',
        'VAT_ELECTRONIC': '增值税电子发票'
      };
      return typeMap[type] || type;
    },
    /** 判断是否为图片URL */
    isImageUrl(url) {
      if (!url || typeof url !== 'string') return false;
      // 检查文件扩展名
      const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i;
      if (imageExtensions.test(url)) return true;
      // 检查是否为base64图片
      if (url.startsWith('data:image/')) return true;
      // 检查是否包含常见的图片服务域名或路径
      const imagePatterns = [
        /\/upload.*\.(jpg|jpeg|png|gif|bmp|webp|svg)/i,
        /\/images?\//i,
        /\/img\//i,
        /\/pic\//i,
        /\/photo\//i
      ];
      return imagePatterns.some(pattern => pattern.test(url));
    },
    /** 处理关联的资金流水编号 */
    async processSourceTransactionIds() {
      this.sourceTransactionList = [];

      if (!this.form.sourceTransactionIds) {
        console.log('没有关联的资金流水ID');
        return;
      }

      try {
        // 解析逗号分隔的ID字符串，处理多个逗号的情况
        const idStrings = this.form.sourceTransactionIds
          .split(',')
          .map(id => id.trim())
          .filter(id => id && /^\d+$/.test(id)); // 只保留非空且为数字的ID

        console.log('解析后的流水ID列表:', idStrings);

        if (idStrings.length === 0) {
          console.log('没有有效的流水ID');
          return;
        }

        // 批量获取资金流水详情
        const promises = idStrings.map(id => getTransaction(parseInt(id)));
        const responses = await Promise.allSettled(promises);

        // 处理响应结果
        const transactions = [];
        responses.forEach((response, index) => {
          if (response.status === 'fulfilled' && response.value && response.value.data) {
            const transaction = response.value.data;
            transactions.push({
              transactionNo: transaction.transactionNo,
              amount: transaction.amount,
              transactionType: transaction.transactionType,
              remark: transaction.remark
            });
          } else {
            console.warn(`获取流水ID ${idStrings[index]} 失败:`, response.reason);
          }
        });

        this.sourceTransactionList = transactions;
        console.log('成功加载关联资金流水:', this.sourceTransactionList);

      } catch (error) {
        console.error('加载关联资金流水失败:', error);
        this.$modal.msgError('加载关联资金流水失败');
      }
    }
  }
};
</script>

<style scoped>
.invoice-image-container {
  margin: 10px 0;
}

.image-slot {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

.image-slot i {
  font-size: 30px;
  margin-bottom: 10px;
}

.image-slot p {
  margin: 0;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}
</style>
